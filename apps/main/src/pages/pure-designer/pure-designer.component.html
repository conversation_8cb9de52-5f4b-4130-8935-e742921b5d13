<div class="app-pure-designer">
  <div class="sider">
    <ul nz-menu nzMode="inline" *ngIf="menuList?.length > 0" [nzSelectable]="false">
      <ng-container *ngFor="let menu of menuList">
        <li nz-submenu [nzOpen]="menu.open" [nzTitle]="menu.name" *ngIf="menu.children && menu.children.length > 0">
          <ul>
            <ng-container *ngFor="let subMenu of menu.children">
              <li
                nz-submenu
                [nzOpen]="subMenu.open"
                [nzTitle]="subMenu.name"
                *ngIf="subMenu.children && subMenu.children.length > 0"
              >
                <ul>
                  <li nz-menu-item *ngFor="let item of subMenu.children" (click)="handleMenuClick(item)">
                    {{ item.name }}
                  </li>
                </ul>
              </li>
              <li
                nz-menu-item
                *ngIf="!subMenu.children || subMenu.children.length === 0"
                (click)="handleMenuClick(subMenu)"
              >
                {{ subMenu.name }}
              </li>
            </ng-container>
          </ul>
        </li>
        <li nz-menu-item *ngIf="!menu.children || menu.children.length === 0" (click)="handleMenuClick(menu)">
          {{ menu.name }}
        </li>
      </ng-container>
    </ul>
  </div>
  <div class="content">
    <router-outlet></router-outlet>
  </div>
</div>
