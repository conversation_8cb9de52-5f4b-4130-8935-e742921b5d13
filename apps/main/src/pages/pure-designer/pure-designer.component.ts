import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { PureDesignerRequestService } from './service/pure-designer-request.service';
import { MenuItem } from './config/pure-designer.type';
import { AppService } from 'pages/apps/app.service';

@Component({
  selector: 'app-pure-designer',
  templateUrl: './pure-designer.component.html',
  styleUrls: ['./pure-designer.component.less'],
  providers: [PureDesignerRequestService],
})
export class PureDesignerComponent implements OnInit {
  menuList: MenuItem[] = [
    {
      name: '作业',
      path: '',
      open: true,
      icon: 'iconjiemiansheji',
      lang: {
        name: {
          zh_CN: '作业',
          zh_TW: '作業',
          en_US: 'Job',
        },
      },
      children: [
        {
          name: '作业1',
          path: '',
          open: true,
          icon: 'iconjiemiansheji',
          lang: {
            name: {
              zh_CN: '作业1',
              zh_TW: '作業1',
              en_US: 'Job1',
            },
          },
          children: [
            {
              name: '页面-1',
              path: 'page-designer',
              open: true,
              icon: 'iconjiemiansheji',
              lang: {
                name: {
                  zh_CN: '页面-1',
                  zh_TW: '頁面-1',
                  en_US: 'Page-1',
                },
              },
              children: [],
            },
            {
              name: '页面-2',
              path: 'page-designer',
              open: true,
              icon: 'iconjiemiansheji',
              lang: {
                name: {
                  zh_CN: '页面-2',
                  zh_TW: '頁面-2',
                  en_US: 'Page-2',
                },
              },
              children: [],
            },
          ],
        },
      ],
    },
  ];

  constructor(private router: Router, private appService: AppService) {}

  ngOnInit(): void {}

  /**
   * 处理菜单项点击事件
   * @param menuItem 菜单项
   */
  handleMenuClick(menuItem: any): void {
    if (menuItem.path) {
      this.router.navigate([`/app/${menuItem.path}`], {
        queryParams: {
          appCode: this.appService?.selectedApp?.code,
        },
      });
    }
  }
}
